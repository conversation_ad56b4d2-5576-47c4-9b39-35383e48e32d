import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: string;
  impact: 'High' | 'Medium' | 'Low';
  effort: 'Easy' | 'Medium' | 'Hard';
  carbonSaving: number;
  icon: string;
  completed: boolean;
}

const RecommendationsScreen: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [recommendations, setRecommendations] = useState<Recommendation[]>([
    {
      id: '1',
      title: 'Use Public Transport',
      description:
        'Take the bus or train instead of driving for your daily commute. This can reduce your transport emissions by up to 45%.',
      category: 'Transport',
      impact: 'High',
      effort: 'Easy',
      carbonSaving: 2.3,
      icon: 'directions-bus',
      completed: false,
    },
    {
      id: '2',
      title: 'Switch to LED Bulbs',
      description:
        'Replace incandescent bulbs with LED lights. They use 75% less energy and last 25 times longer.',
      category: 'Energy',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.8,
      icon: 'lightbulb',
      completed: false,
    },
    {
      id: '3',
      title: 'Reduce Meat Consumption',
      description:
        'Try having one meat-free day per week. Plant-based meals have a significantly lower carbon footprint.',
      category: 'Food',
      impact: 'High',
      effort: 'Medium',
      carbonSaving: 1.5,
      icon: 'eco',
      completed: false,
    },
    {
      id: '4',
      title: 'Unplug Electronics',
      description:
        'Unplug devices when not in use. Electronics in standby mode can account for 10% of your electricity bill.',
      category: 'Energy',
      impact: 'Low',
      effort: 'Easy',
      carbonSaving: 0.4,
      icon: 'power',
      completed: false,
    },
    {
      id: '5',
      title: 'Buy Local Produce',
      description:
        'Choose locally grown fruits and vegetables to reduce transportation emissions and support local farmers.',
      category: 'Food',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.6,
      icon: 'local-grocery-store',
      completed: false,
    },
    {
      id: '6',
      title: 'Use Reusable Bags',
      description:
        'Bring your own bags when shopping to reduce plastic waste and environmental impact.',
      category: 'Shopping',
      impact: 'Low',
      effort: 'Easy',
      carbonSaving: 0.2,
      icon: 'shopping-bag',
      completed: false,
    },
  ]);

  const filters = ['All', 'Transport', 'Energy', 'Food', 'Shopping'];

  const filteredRecommendations = recommendations.filter(
    rec => selectedFilter === 'All' || rec.category === selectedFilter,
  );

  const toggleRecommendation = (id: string) => {
    setRecommendations(prev =>
      prev.map(rec =>
        rec.id === id ? {...rec, completed: !rec.completed} : rec,
      ),
    );
  };

  const getImpactColor = (impact: string): string => {
    switch (impact) {
      case 'High':
        return '#FF5722';
      case 'Medium':
        return '#FF9800';
      case 'Low':
        return '#4CAF50';
      default:
        return '#666';
    }
  };

  const getEffortColor = (effort: string): string => {
    switch (effort) {
      case 'Easy':
        return '#4CAF50';
      case 'Medium':
        return '#FF9800';
      case 'Hard':
        return '#FF5722';
      default:
        return '#666';
    }
  };

  const totalPotentialSaving = recommendations
    .filter(rec => !rec.completed)
    .reduce((sum, rec) => sum + rec.carbonSaving, 0);

  const completedCount = recommendations.filter(rec => rec.completed).length;

  const renderRecommendation = ({item}: {item: Recommendation}) => (
    <TouchableOpacity
      style={[
        styles.recommendationCard,
        item.completed && styles.completedCard,
      ]}
      onPress={() => toggleRecommendation(item.id)}>
      <View style={styles.cardHeader}>
        <View style={styles.iconContainer}>
          <Icon
            name={item.icon}
            size={24}
            color={item.completed ? '#666' : '#4CAF50'}
          />
        </View>
        <View style={styles.cardContent}>
          <Text
            style={[styles.cardTitle, item.completed && styles.completedText]}>
            {item.title}
          </Text>
          <Text
            style={[
              styles.cardDescription,
              item.completed && styles.completedText,
            ]}>
            {item.description}
          </Text>
        </View>
        <View style={styles.checkContainer}>
          <Icon
            name={item.completed ? 'check-circle' : 'radio-button-unchecked'}
            size={24}
            color={item.completed ? '#4CAF50' : '#ccc'}
          />
        </View>
      </View>

      <View style={styles.cardFooter}>
        <View style={styles.tagContainer}>
          <View
            style={[
              styles.tag,
              {backgroundColor: getImpactColor(item.impact) + '20'},
            ]}>
            <Text
              style={[styles.tagText, {color: getImpactColor(item.impact)}]}>
              {item.impact} Impact
            </Text>
          </View>
          <View
            style={[
              styles.tag,
              {backgroundColor: getEffortColor(item.effort) + '20'},
            ]}>
            <Text
              style={[styles.tagText, {color: getEffortColor(item.effort)}]}>
              {item.effort}
            </Text>
          </View>
        </View>
        <View style={styles.savingContainer}>
          <Text style={styles.savingValue}>-{item.carbonSaving} kg</Text>
          <Text style={styles.savingLabel}>CO₂/day</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#4CAF50', '#45A049']} style={styles.header}>
        <Text style={styles.headerTitle}>Eco Recommendations</Text>
        <Text style={styles.headerSubtitle}>
          Potential saving: {totalPotentialSaving.toFixed(1)} kg CO₂/day
        </Text>
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            {completedCount}/{recommendations.length} completed
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {width: `${(completedCount / recommendations.length) * 100}%`},
              ]}
            />
          </View>
        </View>
      </LinearGradient>

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}>
        {filters.map(filter => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterTab,
              selectedFilter === filter && styles.activeFilterTab,
            ]}
            onPress={() => setSelectedFilter(filter)}>
            <Text
              style={[
                styles.filterText,
                selectedFilter === filter && styles.activeFilterText,
              ]}>
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Recommendations List */}
      <FlatList
        data={filteredRecommendations}
        renderItem={renderRecommendation}
        keyExtractor={item => item.id}
        style={styles.recommendationsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
    marginTop: 4,
  },
  progressContainer: {
    marginTop: 16,
  },
  progressText: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 3,
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#fff',
  },
  activeFilterTab: {
    backgroundColor: '#4CAF50',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#fff',
  },
  recommendationsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    opacity: 0.7,
    backgroundColor: '#f8f8f8',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  cardContent: {
    flex: 1,
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: '#999',
  },
  checkContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tagContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  savingContainer: {
    alignItems: 'flex-end',
  },
  savingValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  savingLabel: {
    fontSize: 12,
    color: '#666',
  },
});

export default RecommendationsScreen;
