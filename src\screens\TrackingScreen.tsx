import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

interface Activity {
  id: string;
  category: string;
  description: string;
  amount: number;
  unit: string;
  carbonFootprint: number;
  timestamp: Date;
}

const TrackingScreen: React.FC = () => {
  const [activities, setActivities] = useState<Activity[]>([
    {
      id: '1',
      category: 'Transport',
      description: 'Car drive to work',
      amount: 25,
      unit: 'km',
      carbonFootprint: 2.5,
      timestamp: new Date(),
    },
    {
      id: '2',
      category: 'Energy',
      description: 'Home electricity usage',
      amount: 15,
      unit: 'kWh',
      carbonFootprint: 1.8,
      timestamp: new Date(),
    },
  ]);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');

  const categories = [
    {name: 'Transport', icon: 'directions-car', color: '#FF6B6B'},
    {name: 'Energy', icon: 'flash-on', color: '#4ECDC4'},
    {name: 'Food', icon: 'restaurant', color: '#45B7D1'},
    {name: 'Shopping', icon: 'shopping-cart', color: '#96CEB4'},
  ];

  const calculateCarbonFootprint = (
    category: string,
    activityAmount: number,
  ): number => {
    const factors = {
      Transport: 0.1, // kg CO2 per km
      Energy: 0.12, // kg CO2 per kWh
      Food: 0.5, // kg CO2 per meal
      Shopping: 0.3, // kg CO2 per item
    };
    return activityAmount * (factors[category as keyof typeof factors] || 0);
  };

  const addActivity = () => {
    if (!selectedCategory || !description || !amount) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    const numAmount = parseFloat(amount);
    const carbonFootprint = calculateCarbonFootprint(
      selectedCategory,
      numAmount,
    );

    const newActivity: Activity = {
      id: Date.now().toString(),
      category: selectedCategory,
      description,
      amount: numAmount,
      unit: getUnit(selectedCategory),
      carbonFootprint,
      timestamp: new Date(),
    };

    setActivities([newActivity, ...activities]);
    setModalVisible(false);
    setSelectedCategory('');
    setDescription('');
    setAmount('');
  };

  const getUnit = (category: string): string => {
    const units = {
      Transport: 'km',
      Energy: 'kWh',
      Food: 'meals',
      Shopping: 'items',
    };
    return units[category as keyof typeof units] || '';
  };

  const getCategoryIcon = (category: string): string => {
    const icons = {
      Transport: 'directions-car',
      Energy: 'flash-on',
      Food: 'restaurant',
      Shopping: 'shopping-cart',
    };
    return icons[category as keyof typeof icons] || 'help';
  };

  const getCategoryColor = (category: string): string => {
    const colors = {
      Transport: '#FF6B6B',
      Energy: '#4ECDC4',
      Food: '#45B7D1',
      Shopping: '#96CEB4',
    };
    return colors[category as keyof typeof colors] || '#666';
  };

  const totalCarbonToday = activities.reduce(
    (sum, activity) => sum + activity.carbonFootprint,
    0,
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#4CAF50', '#45A049']} style={styles.header}>
        <Text style={styles.headerTitle}>Carbon Tracking</Text>
        <Text style={styles.headerSubtitle}>
          Today's Total: {totalCarbonToday.toFixed(1)} kg CO₂
        </Text>
      </LinearGradient>

      {/* Add Activity Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setModalVisible(true)}>
        <Icon name="add" size={24} color="#fff" />
        <Text style={styles.addButtonText}>Log New Activity</Text>
      </TouchableOpacity>

      {/* Activities List */}
      <ScrollView style={styles.activitiesList}>
        <Text style={styles.sectionTitle}>Today's Activities</Text>
        {activities.map(activity => (
          <View key={activity.id} style={styles.activityCard}>
            <View style={styles.activityHeader}>
              <View style={styles.activityIconContainer}>
                <Icon
                  name={getCategoryIcon(activity.category)}
                  size={24}
                  color={getCategoryColor(activity.category)}
                />
              </View>
              <View style={styles.activityInfo}>
                <Text style={styles.activityCategory}>{activity.category}</Text>
                <Text style={styles.activityDescription}>
                  {activity.description}
                </Text>
                <Text style={styles.activityAmount}>
                  {activity.amount} {activity.unit}
                </Text>
              </View>
              <View style={styles.carbonInfo}>
                <Text style={styles.carbonValue}>
                  {activity.carbonFootprint.toFixed(1)}
                </Text>
                <Text style={styles.carbonUnit}>kg CO₂</Text>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Add Activity Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Log New Activity</Text>

            {/* Category Selection */}
            <Text style={styles.inputLabel}>Category</Text>
            <View style={styles.categoryGrid}>
              {categories.map(category => (
                <TouchableOpacity
                  key={category.name}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category.name &&
                      styles.categoryButtonSelected,
                  ]}
                  onPress={() => setSelectedCategory(category.name)}>
                  <Icon name={category.icon} size={24} color={category.color} />
                  <Text style={styles.categoryButtonText}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Description Input */}
            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Drive to work"
              value={description}
              onChangeText={setDescription}
            />

            {/* Amount Input */}
            <Text style={styles.inputLabel}>
              Amount ({selectedCategory ? getUnit(selectedCategory) : ''})
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder="Enter amount"
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
            />

            {/* Modal Buttons */}
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={addActivity}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
    marginTop: 4,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  activitiesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  activityCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  activityCategory: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activityDescription: {
    fontSize: 16,
    color: '#333',
    marginVertical: 2,
  },
  activityAmount: {
    fontSize: 14,
    color: '#666',
  },
  carbonInfo: {
    alignItems: 'flex-end',
  },
  carbonValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  carbonUnit: {
    fontSize: 12,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    margin: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryButton: {
    width: '48%',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryButtonSelected: {
    backgroundColor: '#E8F5E8',
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#333',
    marginTop: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  modalButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TrackingScreen;
